from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime

class ChatMessage(BaseModel):
    role: str = Field(..., description="Role of the message sender (user/assistant)")
    content: str = Field(..., description="Content of the message")
    timestamp: datetime = Field(default_factory=datetime.now)

class ChatRequest(BaseModel):
    message: str = Field(..., description="User's message to the chatbot")
    conversation_id: Optional[str] = Field(None, description="Conversation ID for context")
    location: Optional[str] = Field(None, description="User's location for weather context")
    crop_type: Optional[str] = Field(None, description="Current crop being discussed")

class ChatResponse(BaseModel):
    response: str = Field(..., description="Chatbot's response")
    conversation_id: str = Field(..., description="Conversation ID")
    suggestions: Optional[List[str]] = Field(None, description="Follow-up suggestions")
    weather_context: Optional[Dict[str, Any]] = Field(None, description="Weather information used")

class WeatherRequest(BaseModel):
    location: str = Field(..., description="Location for weather data")
    
class WeatherResponse(BaseModel):
    location: str
    temperature: float
    humidity: float
    description: str
    wind_speed: float
    pressure: float
    feels_like: float
    timestamp: datetime = Field(default_factory=datetime.now)

class CropAdviceRequest(BaseModel):
    crop_type: str = Field(..., description="Type of crop")
    growth_stage: Optional[str] = Field(None, description="Current growth stage")
    location: Optional[str] = Field(None, description="Location for weather context")
    issues: Optional[List[str]] = Field(None, description="Current issues or concerns")

class CropAdviceResponse(BaseModel):
    crop_type: str
    recommendations: List[str]
    weather_considerations: Optional[str] = None
    next_steps: List[str]
    warnings: Optional[List[str]] = None

class MarketInfo(BaseModel):
    crop: str
    current_price: Optional[float] = None
    price_trend: Optional[str] = None
    demand_level: Optional[str] = None
    best_markets: Optional[List[str]] = None
    harvest_timing: Optional[str] = None
