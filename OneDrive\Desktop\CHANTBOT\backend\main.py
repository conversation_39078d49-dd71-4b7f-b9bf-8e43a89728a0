from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from dotenv import load_dotenv
import os
import uvicorn

# Load environment variables
load_dotenv()

# Import routers
from routers import chat, weather, agriculture

app = FastAPI(
    title="Agricultural Chatbot API",
    description="AI-driven chatbot for small-scale farmers",
    version="1.0.0"
)

# Configure CORS
origins = os.getenv("CORS_ORIGINS", "http://localhost:5173").split(",")

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(chat.router, prefix="/api/chat", tags=["chat"])
app.include_router(weather.router, prefix="/api/weather", tags=["weather"])
app.include_router(agriculture.router, prefix="/api/agriculture", tags=["agriculture"])

@app.get("/")
async def root():
    return {"message": "Agricultural Chatbot API is running"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "agricultural-chatbot"}

if __name__ == "__main__":
    host = os.getenv("HOST", "localhost")
    port = int(os.getenv("PORT", 8000))
    uvicorn.run(app, host=host, port=port, reload=True)
