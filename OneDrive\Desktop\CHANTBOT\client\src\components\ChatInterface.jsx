import { useState, useRef, useEffect } from 'react'
import { Send, Mic, Mic<PERSON><PERSON>, User, Bo<PERSON> } from 'lucide-react'
import axios from 'axios'

const ChatInterface = () => {
  const [messages, setMessages] = useState([
    {
      role: 'assistant',
      content: 'Hello! I\'m <PERSON><PERSON><PERSON><PERSON>, your agricultural assistant. I can help you with crop management, weather-based farming advice, pest control, market information, and income optimization strategies.\n\nYou can mention your location in your message (e.g., "I\'m in Punjab, India and need help with rice farming") for location-specific advice. How can I assist you today?',
      timestamp: new Date()
    }
  ])
  const [inputMessage, setInputMessage] = useState('')
  const [location, setLocation] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isRecording, setIsRecording] = useState(false)
  const [conversationId, setConversationId] = useState(null)
  const [suggestions, setSuggestions] = useState([])
  const [error, setError] = useState(null)
  
  const messagesEndRef = useRef(null)
  const mediaRecorderRef = useRef(null)
  const audioChunksRef = useRef([])

  const API_BASE_URL = 'http://localhost:8000/api'

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  const extractLocationFromMessage = (message) => {
    // Simple location extraction - looks for common patterns
    const locationPatterns = [
      /(?:in|from|at)\s+([A-Za-z\s,]+(?:India|Pakistan|Bangladesh|Nepal|Sri Lanka))/i,
      /(?:in|from|at)\s+([A-Za-z\s,]+(?:state|province|district|city))/i,
      /(?:in|from|at)\s+([A-Za-z\s,]+)/i
    ];

    for (const pattern of locationPatterns) {
      const match = message.match(pattern);
      if (match) {
        return match[1].trim();
      }
    }
    return location; // fallback to stored location
  };

  const handleSendMessage = async (messageText = inputMessage) => {
    if (!messageText.trim()) return

    const userMessage = {
      role: 'user',
      content: messageText,
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    setInputMessage('')
    setIsLoading(true)
    setError(null)

    // Extract location from message if present
    const extractedLocation = extractLocationFromMessage(messageText);
    if (extractedLocation && extractedLocation !== location) {
      setLocation(extractedLocation);
    }

    try {
      const response = await axios.post(`${API_BASE_URL}/chat/`, {
        message: messageText,
        conversation_id: conversationId,
        location: extractedLocation || null,
        crop_type: null // Could be extracted from message or set separately
      })

      const assistantMessage = {
        role: 'assistant',
        content: response.data.response,
        timestamp: new Date()
      }

      setMessages(prev => [...prev, assistantMessage])
      setConversationId(response.data.conversation_id)
      setSuggestions(response.data.suggestions || [])

    } catch (error) {
      console.error('Error sending message:', error)
      setError('Failed to send message. Please check if the backend server is running.')
      
      // Add error message to chat
      const errorMessage = {
        role: 'assistant',
        content: 'Sorry, I\'m having trouble connecting to the server. Please make sure the backend is running and try again.',
        timestamp: new Date()
      }
      setMessages(prev => [...prev, errorMessage])
    } finally {
      setIsLoading(false)
    }
  }

  const handleSuggestionClick = (suggestion) => {
    handleSendMessage(suggestion)
    setSuggestions([])
  }

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
      const mediaRecorder = new MediaRecorder(stream)
      mediaRecorderRef.current = mediaRecorder
      audioChunksRef.current = []

      mediaRecorder.ondataavailable = (event) => {
        audioChunksRef.current.push(event.data)
      }

      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/wav' })
        // Here you would typically send the audio to a speech-to-text service
        // For now, we'll just show a placeholder message
        setInputMessage('Voice message recorded (Speech-to-text integration needed)')
        stream.getTracks().forEach(track => track.stop())
      }

      mediaRecorder.start()
      setIsRecording(true)
    } catch (error) {
      console.error('Error accessing microphone:', error)
      setError('Could not access microphone. Please check permissions.')
    }
  }

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop()
      setIsRecording(false)
    }
  }

  const handleVoiceToggle = () => {
    if (isRecording) {
      stopRecording()
    } else {
      startRecording()
    }
  }

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  return (
    <div className="chat-container">
      <div className="chat-messages">
        {error && (
          <div className="error-message">
            {error}
          </div>
        )}
        
        {messages.map((message, index) => (
          <div key={index} className={`message ${message.role}`}>
            <div className="message-avatar">
              {message.role === 'user' ? <User size={16} /> : <Bot size={16} />}
            </div>
            <div className="message-content">
              {message.content}
            </div>
          </div>
        ))}

        {isLoading && (
          <div className="message assistant">
            <div className="message-avatar">
              <Bot size={16} />
            </div>
            <div className="typing-indicator">
              <span>ChantBot is thinking</span>
              <div className="typing-dots">
                <div className="typing-dot"></div>
                <div className="typing-dot"></div>
                <div className="typing-dot"></div>
              </div>
            </div>
          </div>
        )}

        {suggestions.length > 0 && !isLoading && (
          <div className="suggestions">
            {suggestions.map((suggestion, index) => (
              <button
                key={index}
                className="suggestion-chip"
                onClick={() => handleSuggestionClick(suggestion)}
              >
                {suggestion}
              </button>
            ))}
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      <div className="chat-input-container">
        <form className="chat-input-form" onSubmit={(e) => { e.preventDefault(); handleSendMessage(); }}>
          <div className="input-group">
            <textarea
              className="message-input"
              placeholder="Ask me about crops, weather, pests, market prices, or farming techniques... (You can mention your location in the message for location-specific advice)"
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              rows={2}
            />
            {location && (
              <div className="location-display">
                📍 Current location: {location}
              </div>
            )}
          </div>

          <button
            type="button"
            className={`voice-button ${isRecording ? 'recording' : ''}`}
            onClick={handleVoiceToggle}
            title={isRecording ? 'Stop recording' : 'Start voice recording'}
          >
            {isRecording ? <MicOff size={20} /> : <Mic size={20} />}
          </button>

          <button
            type="submit"
            className="send-button"
            disabled={!inputMessage.trim() || isLoading}
            title="Send message"
          >
            <Send size={20} />
          </button>
        </form>
      </div>
    </div>
  )
}

export default ChatInterface
